import 'package:flutter/material.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';

AppBar settingsAppBar(String title, BuildContext context) {
  final theme = Theme.of(context);
  return AppBar(
    leading: I<PERSON><PERSON><PERSON>on(
      icon: Icon(
        AdaptiveIcons.back,
        semanticLabel: 'Back to previous settings',
      ),
      tooltip: 'Back',
      onPressed: () {
        Navigator.pop(context);
      },
    ),
    title: Text(
      title,
      style: theme.textTheme.titleLarge?.copyWith(
        fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w500),
        color: DesignSystem.getSettingsTextColor(context, isPrimary: true),
      ),
    ),
    backgroundColor: theme.appBarTheme.backgroundColor,
    foregroundColor: theme.appBarTheme.foregroundColor,
    elevation: DesignSystem.getAdjustedElevation(0.0),
    centerTitle: false,
    titleSpacing: DesignSystem.spaceS,
  );
}
